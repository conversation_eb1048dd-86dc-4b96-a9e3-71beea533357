/**
 * @file: middleware.ts
 * @description: Функции для middleware (без PDF-генерации)
 * @dependencies: @supabase/ssr, next/headers
 * @created: 2025-01-26
 */

import { createServerClient } from '@supabase/ssr'
import { NextRequest, NextResponse } from 'next/server'

/**
 * Конфигурация для различных типов клиентов Supabase
 */
export const AUTH_CONFIG = {
  // URL и ключи из переменных окружения
  url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,

  // Роли и их приоритеты
  roles: {
    superadmin: { priority: 1, defaultRedirect: '/admin/dashboard' },
    support: { priority: 2, defaultRedirect: '/admin/dashboard' },
    tenant_admin: { priority: 3, defaultRedirect: '/dashboard' },
    after_sales_manager: { priority: 4, defaultRedirect: '/dashboard' },
    client: { priority: 5, defaultRedirect: '/client/dashboard' },
  },
} as const

/**
 * Создает клиент Supabase для middleware
 */
export function createMiddlewareSupabaseClient(req: NextRequest, res: NextResponse) {
  return createServerClient(AUTH_CONFIG.url, AUTH_CONFIG.anonKey, {
    cookies: {
      get(name: string) {
        return req.cookies.get(name)?.value
      },
      set(name: string, value: string, options: any) {
        res.cookies.set({
          name,
          value,
          ...options,
        })
      },
      remove(name: string, options: any) {
        res.cookies.set({
          name,
          value: '',
          ...options,
        })
      },
    },
  })
}

/**
 * Получает роль пользователя из сессии
 */
export function getUserRole(session: any): string | null {
  if (!session?.user) return null

  const user = session.user
  // Проверяем все возможные места расположения роли
  return user.user_metadata?.role || user.raw_user_meta_data?.role || user.app_metadata?.role || null
}

/**
 * Получает tenant_id пользователя из сессии
 */
export function getUserTenantId(session: any): string | null {
  if (!session?.user) return null

  const user = session.user
  // Проверяем все возможные места расположения tenant_id
  return user.user_metadata?.tenant_id || user.raw_user_meta_data?.tenant_id || user.app_metadata?.tenant_id || null
}

/**
 * Проверяет, имеет ли пользователь доступ к маршруту
 */
export function hasRouteAccess(userRole: string | null, pathname: string): boolean {
  if (!userRole) return false

  // Маршруты для SuperAdmin и Support
  if (pathname.startsWith('/admin')) {
    return userRole === 'superadmin' || userRole === 'support'
  }

  // Маршруты для Tenant Admin и After Sales Manager
  if (pathname.startsWith('/dashboard')) {
    return userRole === 'tenant_admin' || userRole === 'after_sales_manager'
  }

  // Маршруты для клиентов
  if (pathname.startsWith('/client')) {
    return userRole === 'client'
  }

  return true
}

/**
 * Получает URL для перенаправления на основе роли пользователя
 */
export function getRedirectUrl(userRole: string | null): string {
  if (!userRole || !(userRole in AUTH_CONFIG.roles)) {
    return '/login'
  }

  return AUTH_CONFIG.roles[userRole as keyof typeof AUTH_CONFIG.roles].defaultRedirect
}

/**
 * Типы для TypeScript
 */
export type UserRole = keyof typeof AUTH_CONFIG.roles
export type AuthConfig = typeof AUTH_CONFIG
