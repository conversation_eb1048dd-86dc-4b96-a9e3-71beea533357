'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Login03 } from '../../components/ui-wrappers'

/**
 * Страница авторизации для TenantDashboard
 *
 * Проверяет роль пользователя после успешной авторизации и перенаправляет только
 * пользователей с ролями tenant_admin или after_sales_manager. Для других пользователей
 * показывает ошибку и выполняет выход из системы.
 *
 * @returns Компонент страницы авторизации TenantDashboard
 */
export default function TenantLoginPage() {
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const supabase = createClientComponentClient()

  const handleLogin = async (data: { email: string; password: string }) => {
    setLoading(true)
    setError(null)

    try {
      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      })

      if (error) {
        throw error
      }

      // Проверяем роль пользователя из всех возможных источников
      const user = authData.user;
      const role = user?.user_metadata?.role || (user as any)?.raw_user_meta_data?.role || user?.app_metadata?.role;

      console.log('🔍 TenantDashboard Login: Данные пользователя:', {
        id: user?.id,
        email: user?.email,
        user_metadata: user?.user_metadata,
        raw_user_meta_data: (user as any)?.raw_user_meta_data,
        app_metadata: user?.app_metadata,
        extractedRole: role
      });

      if (role !== 'tenant_admin' && role !== 'after_sales_manager') {
        console.log(`❌ TenantDashboard Login: Недостаточно прав. Роль: ${role}`);
        // Если пользователь не tenant_admin или after_sales_manager, выходим из системы
        await supabase.auth.signOut()
        setError('У вас нет доступа к панели застройщика')
        setLoading(false)
        return
      }

      console.log(`✅ TenantDashboard Login: Роль подтверждена: ${role}`);

      // Даем время для установки cookies, затем принудительно перенаправляем
      setTimeout(() => {
        window.location.href = '/dashboard';
      }, 500);
    } catch (error: any) {
      setError(error.message || 'Произошла ошибка при входе')
    } finally {
      setLoading(false)
    }
  }

  const handleForgotPassword = () => {
    router.push('/forgot-password')
  }

  return (
    <Login03
      onSubmit={handleLogin}
      loading={loading}
      error={error || undefined}
      title="Панель застройщика"
      subtitle="Войдите в систему управления недвижимостью"
      companyName="PactCRM"
      showForgotPassword={true}
      onForgotPassword={handleForgotPassword}
    />
  )
}
