/**
 * @file: middleware.ts
 * @description: Middleware для проверки аутентификации и перенаправления пользователей
 * @dependencies: next, @supabase/auth-helpers-nextjs
 * @created: 2023-12-01
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { createMiddlewareSupabaseClient, getUserRole, hasRouteAccess } from '@pactcrm/supabase-client/dist/middleware/middleware';

/**
 * Middleware для проверки аутентификации и перенаправления пользователей
 *
 * Проверяет наличие сессии и роли пользователя, перенаправляя на соответствующие страницы
 * в зависимости от прав доступа
 *
 * @param req Запрос Next.js
 * @returns Ответ Next.js (перенаправление или продолжение)
 */
export async function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const supabase = createMiddlewareSupabaseClient(req, res);
  const { pathname } = req.nextUrl;

  console.log(`🔍 TenantDashboard Middleware: ${req.method} ${pathname}`);

  // Проверяем сессию
  const { data: { session } } = await supabase.auth.getSession();
  console.log('🔍 TenantDashboard Middleware: Сессия получена:', !!session);

  // Публичные маршруты, доступные без аутентификации
  const publicRoutes = ['/login', '/register', '/reset-password', '/client/login'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  // Маршруты для клиентов
  const clientRoutes = ['/client/dashboard'];
  const isClientRoute = clientRoutes.some(route => pathname.startsWith(route));

  // Маршруты для администраторов
  const adminRoutes = ['/dashboard', '/properties', '/clients', '/contracts', '/payments', '/settings', '/components-demo'];
  const isAdminRoute = adminRoutes.some(route => pathname.startsWith(route));

  // Если пользователь не аутентифицирован и пытается получить доступ к защищенному маршруту
  if (!session && !isPublicRoute) {
    const redirectUrl = new URL('/login', req.url);
    return NextResponse.redirect(redirectUrl);
  }

  // Если пользователь аутентифицирован
  if (session) {
    console.log('🔍 TenantDashboard Middleware: Пользователь аутентифицирован, email:', session.user?.email);
    console.log('🔍 TenantDashboard Middleware: Данные пользователя:', {
      id: session.user?.id,
      email: session.user?.email,
      user_metadata: session.user?.user_metadata,
      raw_user_meta_data: session.user?.raw_user_meta_data,
      app_metadata: session.user?.app_metadata
    });

    const role = getUserRole(session);
    console.log('🔍 TenantDashboard Middleware: Роль пользователя:', role);

    // Проверяем доступ к маршруту
    const hasAccess = hasRouteAccess(role, pathname);
    console.log('🔍 TenantDashboard Middleware: Доступ к маршруту', pathname, ':', hasAccess);

    if (!hasAccess) {
      // Перенаправляем на соответствующую панель в зависимости от роли
      if (role === 'superadmin' || role === 'support') {
        const redirectUrl = new URL('/admin/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      } else if (role === 'tenant_admin' || role === 'after_sales_manager') {
        console.log('🔍 TenantDashboard Middleware: Перенаправление tenant_admin/after_sales_manager на /dashboard');
        const redirectUrl = new URL('/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      } else if (role === 'client') {
        console.log('🔍 TenantDashboard Middleware: Перенаправление client на /client/dashboard');
        const redirectUrl = new URL('/client/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      } else {
        // Неизвестная роль - выходим
        console.log('🔍 TenantDashboard Middleware: Неизвестная роль, перенаправление на /login');
        const redirectUrl = new URL('/login', req.url);
        return NextResponse.redirect(redirectUrl);
      }
    }

    // Если пользователь на странице входа, перенаправляем на соответствующую панель
    if (isPublicRoute && pathname !== '/client/login') {
      if (role === 'superadmin' || role === 'support') {
        const redirectUrl = new URL('/admin/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      } else if (role === 'tenant_admin' || role === 'after_sales_manager') {
        const redirectUrl = new URL('/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      } else if (role === 'client') {
        const redirectUrl = new URL('/client/dashboard', req.url);
        return NextResponse.redirect(redirectUrl);
      }
    }
  }

  return res;
}

/**
 * Конфигурация middleware
 *
 * Указывает, для каких маршрутов должен применяться middleware
 */
export const config = {
  matcher: [
    // Временно отключаем middleware для тестирования
    // '/dashboard/:path*',
    // '/properties/:path*',
    // '/clients/:path*',
    // '/contracts/:path*',
    // '/payments/:path*',
    // '/settings/:path*',
    // '/components-demo/:path*',
    // '/client/dashboard/:path*',
    // Публичные маршруты для проверки перенаправления
    // '/login',
    // '/register',
    // '/reset-password',
    // '/client/login',
  ],
};
